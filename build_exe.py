#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بناء ملف تنفيذي للبرنامج
Build script for creating executable file
"""

import os
import sys
import shutil
from pathlib import Path

def build_executable():
    """بناء ملف تنفيذي باستخدام PyInstaller"""
    
    print("🔨 بدء عملية بناء الملف التنفيذي...")
    
    # التأكد من وجود PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("❌ PyInstaller غير مثبت. يرجى تثبيته أولاً:")
        print("pip install pyinstaller")
        return False
    
    # إعدادات البناء
    app_name = "AlHassanStone"
    main_file = "main.py"
    icon_file = "assets/icons/app_icon.ico"  # يجب تحويل PNG إلى ICO
    
    # أوامر PyInstaller
    cmd_parts = [
        "pyinstaller",
        "--name", app_name,
        "--onefile",  # ملف واحد
        "--windowed",  # بدون نافذة الكونسول
        "--clean",  # تنظيف الملفات المؤقتة
        f"--distpath=dist",  # مجلد الإخراج
        f"--workpath=build",  # مجلد العمل
        f"--specpath=.",  # مجلد ملف spec
    ]
    
    # إضافة الأيقونة إذا كانت موجودة
    if os.path.exists(icon_file):
        cmd_parts.extend(["--icon", icon_file])
    
    # إضافة الملفات والمجلدات المطلوبة
    data_files = [
        ("assets", "assets"),
        ("src", "src"),
    ]
    
    for src, dst in data_files:
        if os.path.exists(src):
            cmd_parts.extend(["--add-data", f"{src};{dst}"])
    
    # إضافة الملف الرئيسي
    cmd_parts.append(main_file)
    
    # تنفيذ الأمر
    cmd = " ".join(cmd_parts)
    print(f"🔧 تنفيذ الأمر: {cmd}")
    
    result = os.system(cmd)
    
    if result == 0:
        print("✅ تم بناء الملف التنفيذي بنجاح!")
        print(f"📁 الملف موجود في: dist/{app_name}.exe")
        
        # نسخ الملفات المطلوبة
        copy_required_files()
        
        return True
    else:
        print("❌ فشل في بناء الملف التنفيذي")
        return False

def copy_required_files():
    """نسخ الملفات المطلوبة لمجلد التوزيع"""
    
    dist_dir = Path("dist")
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ["data", "reports", "backups"]
    for dir_name in required_dirs:
        (dist_dir / dir_name).mkdir(exist_ok=True)
    
    # نسخ ملف الإعدادات
    if os.path.exists("config.json"):
        shutil.copy2("config.json", dist_dir / "config.json")
    
    # نسخ ملف README
    if os.path.exists("README.md"):
        shutil.copy2("README.md", dist_dir / "README.md")
    
    print("📋 تم نسخ الملفات المطلوبة")

def create_installer():
    """إنشاء مثبت للبرنامج (اختياري)"""
    
    # يمكن استخدام NSIS أو Inno Setup لإنشاء مثبت
    # هذا مثال بسيط لإنشاء ملف batch للتثبيت
    
    installer_content = """@echo off
echo Installing Al-Hassan Stone Management System...
echo.

REM Create application directory
if not exist "C:\\Program Files\\AlHassanStone" mkdir "C:\\Program Files\\AlHassanStone"

REM Copy files
xcopy /E /I /Y "." "C:\\Program Files\\AlHassanStone\\"

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\Al-Hassan Stone.lnk'); $Shortcut.TargetPath = 'C:\\Program Files\\AlHassanStone\\AlHassanStone.exe'; $Shortcut.Save()"

echo.
echo Installation completed successfully!
echo You can now run the application from the desktop shortcut.
pause
"""
    
    with open("dist/install.bat", "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print("📦 تم إنشاء ملف التثبيت: dist/install.bat")

def main():
    """الدالة الرئيسية"""
    
    print("=" * 50)
    print("🏭 برنامج إدارة مصنع الحسن ستون")
    print("   Al-Hassan Stone Management System")
    print("=" * 50)
    print()
    
    # التحقق من وجود الملفات المطلوبة
    if not os.path.exists("main.py"):
        print("❌ الملف الرئيسي main.py غير موجود")
        return
    
    # بناء الملف التنفيذي
    if build_executable():
        
        # إنشاء مثبت (اختياري)
        create_installer()
        
        print()
        print("🎉 تم الانتهاء من عملية البناء بنجاح!")
        print()
        print("📁 الملفات المُنشأة:")
        print(f"   - dist/AlHassanStone.exe (الملف التنفيذي)")
        print(f"   - dist/install.bat (ملف التثبيت)")
        print()
        print("💡 لتشغيل البرنامج:")
        print("   1. انتقل إلى مجلد dist")
        print("   2. شغل ملف AlHassanStone.exe")
        print()
        print("💡 للتثبيت على الكمبيوتر:")
        print("   1. انتقل إلى مجلد dist")
        print("   2. شغل ملف install.bat كمدير")
        
    else:
        print("❌ فشل في بناء البرنامج")

if __name__ == "__main__":
    main()
