#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط للبرنامج
Simple run script for the application
"""

import sys
import os

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    # التحقق من إصدار Python
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # التحقق من PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 متاح")
    except ImportError:
        print("❌ PyQt5 غير مثبت")
        print("لتثبيته: pip install PyQt5")
        return False
    
    return True

def setup_environment():
    """إعداد البيئة"""
    # إضافة مسار المشروع
    project_path = os.path.dirname(os.path.abspath(__file__))
    if project_path not in sys.path:
        sys.path.insert(0, project_path)
    
    # إنشاء المجلدات المطلوبة
    required_dirs = ["data", "reports", "backups"]
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"📁 تم إنشاء مجلد: {dir_name}")

def run_application():
    """تشغيل التطبيق"""
    try:
        print("🚀 تشغيل التطبيق...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إعداد اللغة العربية
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد الخط
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        # تطبيق الأنماط إذا كانت متاحة
        style_file = "assets/styles/main_style.qss"
        if os.path.exists(style_file):
            try:
                with open(style_file, "r", encoding="utf-8") as f:
                    app.setStyleSheet(f.read())
                print("✅ تم تطبيق الأنماط")
            except Exception as e:
                print(f"⚠️  تحذير: لم يتم تطبيق الأنماط - {e}")
        
        # تهيئة قاعدة البيانات
        from src.database.database_manager import DatabaseManager
        db = DatabaseManager()
        if not db.initialize_database():
            print("❌ فشل في تهيئة قاعدة البيانات")
            return 1
        
        print("✅ تم تهيئة قاعدة البيانات")
        
        # إنشاء نافذة تسجيل الدخول
        from src.ui.login_window import LoginWindow
        login_window = LoginWindow()
        login_window.show()
        
        print("✅ تم فتح نافذة تسجيل الدخول")
        print("📝 بيانات الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: 123456")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من تثبيت جميع المتطلبات: pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏭 مصنع الحسن ستون للجرانيت")
    print("   Al-Hassan Stone Granite Factory Management System")
    print("=" * 60)
    print()
    
    # التحقق من المتطلبات
    if not check_requirements():
        return 1
    
    # إعداد البيئة
    setup_environment()
    
    # تشغيل التطبيق
    return run_application()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
