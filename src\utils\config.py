# -*- coding: utf-8 -*-
"""
ملف إعدادات التطبيق
Application configuration file
"""

import os
import json
from typing import Dict, Any

class Config:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "database": {
                "type": "sqlite",
                "path": "data/alhassan_stone.db",
                "backup_path": "backups/"
            },
            "ui": {
                "theme": "dark",
                "language": "ar",
                "font_family": "Segoe UI",
                "font_size": 10,
                "window_size": {
                    "width": 1200,
                    "height": 800
                }
            },
            "reports": {
                "output_path": "reports/",
                "company_info": {
                    "name": "مصنع الحسن ستون للجرانيت",
                    "name_en": "Al-Hassan Stone Granite Factory",
                    "address": "",
                    "phone": "",
                    "email": "",
                    "logo_path": "assets/icons/company_logo.png"
                }
            },
            "security": {
                "session_timeout": 3600,  # ساعة واحدة
                "max_login_attempts": 3,
                "password_min_length": 6
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع الإعدادات المحملة
                return self._merge_configs(self.default_config, config)
            else:
                # إنشاء ملف الإعدادات الافتراضي
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None) -> bool:
        """حفظ الإعدادات في الملف"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False
    
    def get(self, key_path: str, default=None):
        """الحصول على قيمة إعداد باستخدام مسار النقاط"""
        keys = key_path.split('.')
        value = self.config
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set(self, key_path: str, value: Any) -> bool:
        """تعيين قيمة إعداد باستخدام مسار النقاط"""
        keys = key_path.split('.')
        config = self.config
        
        # التنقل إلى المستوى الأخير
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # تعيين القيمة
        config[keys[-1]] = value
        
        # حفظ الإعدادات
        return self.save_config()
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """دمج الإعدادات الافتراضية مع المحملة"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result

# إنشاء مثيل عام للإعدادات
app_config = Config()
