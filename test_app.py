#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للتطبيق
Simple application test
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5 متاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt5: {e}")
        return False
    
    try:
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager متاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد DatabaseManager: {e}")
        return False
    
    try:
        from src.models.user import User
        print("✅ User model متاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد User: {e}")
        return False
    
    try:
        from src.utils.config import app_config
        print("✅ Config متاح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد Config: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️  اختبار قاعدة البيانات...")
    
    try:
        from src.database.database_manager import DatabaseManager
        
        db = DatabaseManager()
        if db.initialize_database():
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
            
            # اختبار إدراج مستخدم
            from src.models.user import User
            test_user = User(
                username="test_user",
                password="test_pass",
                full_name="مستخدم تجريبي",
                role="user"
            )
            
            if test_user.save():
                print("✅ تم إنشاء مستخدم تجريبي")
                
                # اختبار تسجيل الدخول
                auth_user = User.authenticate("test_user", "test_pass")
                if auth_user:
                    print("✅ تم التحقق من المستخدم بنجاح")
                    return True
                else:
                    print("❌ فشل في التحقق من المستخدم")
                    return False
            else:
                print("❌ فشل في إنشاء المستخدم")
                return False
        else:
            print("❌ فشل في تهيئة قاعدة البيانات")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui():
    """اختبار واجهة المستخدم"""
    print("\n🖥️  اختبار واجهة المستخدم...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.ui.login_window import LoginWindow
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow()
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # لا نعرض النافذة في الاختبار
        # login_window.show()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🧪 اختبار برنامج الحسن ستون")
    print("   Al-Hassan Stone Test Suite")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # اختبار الاستيرادات
    if test_imports():
        tests_passed += 1
    
    # اختبار قاعدة البيانات
    if test_database():
        tests_passed += 1
    
    # اختبار واجهة المستخدم
    if test_ui():
        tests_passed += 1
    
    print(f"\n📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {tests_passed}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("\n🎉 جميع الاختبارات نجحت! البرنامج جاهز للتشغيل.")
        return True
    else:
        print("\n⚠️  بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
