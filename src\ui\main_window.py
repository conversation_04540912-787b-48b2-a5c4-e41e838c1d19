# -*- coding: utf-8 -*-
"""
النافذة الرئيسية
Main Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                            QStackedWidget, QLabel, QPushButton, QFrame,
                            QScrollArea, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

from src.ui.dashboard import DashboardWidget
from src.ui.sidebar import SidebarWidget

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    # إشارة إغلاق النافذة
    closed = pyqtSignal()
    
    def __init__(self, user):
        super().__init__()
        self.current_user = user
        self.current_page = "dashboard"
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"الحسن ستون - {self.current_user.full_name}")
        self.setMinimumSize(1200, 800)
        self.setWindowState(Qt.WindowMaximized)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.sidebar = SidebarWidget(self.current_user)
        self.sidebar.page_changed.connect(self.change_page)
        
        # منطقة المحتوى الرئيسي
        content_area = QFrame()
        content_area.setObjectName("contentArea")
        
        # تخطيط منطقة المحتوى
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # شريط العنوان العلوي
        self.title_bar = self.create_title_bar()
        
        # منطقة الصفحات
        self.pages_stack = QStackedWidget()
        self.pages_stack.setObjectName("pagesStack")
        
        # إضافة الصفحات
        self.setup_pages()
        
        # إضافة العناصر لتخطيط المحتوى
        content_layout.addWidget(self.title_bar)
        content_layout.addWidget(self.pages_stack)
        
        # إضافة العناصر للتخطيط الرئيسي
        main_layout.addWidget(self.sidebar)
        main_layout.addWidget(content_area)
        
        # تعيين نسب العرض
        main_layout.setStretch(0, 0)  # الشريط الجانبي
        main_layout.setStretch(1, 1)  # منطقة المحتوى
    
    def create_title_bar(self):
        """إنشاء شريط العنوان العلوي"""
        title_bar = QFrame()
        title_bar.setObjectName("titleBar")
        title_bar.setFixedHeight(60)
        
        layout = QHBoxLayout(title_bar)
        layout.setContentsMargins(20, 10, 20, 10)
        
        # عنوان الصفحة الحالية
        self.page_title = QLabel("لوحة التحكم")
        self.page_title.setObjectName("pageTitle")
        
        # معلومات المستخدم
        user_info = QLabel(f"مرحباً، {self.current_user.full_name}")
        user_info.setObjectName("userInfo")
        
        # زر تسجيل الخروج
        logout_button = QPushButton("تسجيل الخروج")
        logout_button.setObjectName("logoutButton")
        logout_button.clicked.connect(self.logout)
        
        # إضافة العناصر
        layout.addWidget(self.page_title)
        layout.addStretch()
        layout.addWidget(user_info)
        layout.addWidget(logout_button)
        
        return title_bar
    
    def setup_pages(self):
        """إعداد صفحات التطبيق"""
        # لوحة التحكم
        self.dashboard = DashboardWidget(self.current_user)
        self.pages_stack.addWidget(self.dashboard)
        
        # صفحات أخرى (سيتم إضافتها لاحقاً)
        # self.clients_page = ClientsWidget()
        # self.pages_stack.addWidget(self.clients_page)
        
        # تعيين الصفحة الافتراضية
        self.pages_stack.setCurrentWidget(self.dashboard)
    
    def change_page(self, page_name: str):
        """تغيير الصفحة الحالية"""
        page_titles = {
            "dashboard": "لوحة التحكم",
            "clients": "العملاء والموردين",
            "invoices": "فواتير البيع",
            "trucks": "دخول الجرارات",
            "slicing": "عمليات النشر",
            "inventory": "إدارة المخزون",
            "expenses": "المصاريف",
            "reports": "التقارير",
            "settings": "الإعدادات"
        }
        
        self.current_page = page_name
        self.page_title.setText(page_titles.get(page_name, "صفحة غير معروفة"))
        
        # تغيير الصفحة في المكدس
        if page_name == "dashboard":
            self.pages_stack.setCurrentWidget(self.dashboard)
            # تحديث البيانات
            self.dashboard.refresh_data()
        else:
            # صفحات أخرى (سيتم تنفيذها لاحقاً)
            self.show_message("قريباً", f"صفحة {page_titles.get(page_name)} قيد التطوير")
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, 'تأكيد تسجيل الخروج',
            'هل تريد تسجيل الخروج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
    
    def show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """عرض رسالة للمستخدم"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setLayoutDirection(Qt.RightToLeft)
        msg_box.exec_()
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        #contentArea {
            background-color: #2d2d2d;
            border-left: 1px solid #3d3d3d;
        }
        
        #titleBar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3d3d3d, stop:1 #2d2d2d);
            border-bottom: 1px solid #4d4d4d;
        }
        
        #pageTitle {
            font-size: 20px;
            font-weight: bold;
            color: #4a9eff;
        }
        
        #userInfo {
            font-size: 14px;
            color: #cccccc;
            margin-right: 20px;
        }
        
        #logoutButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #d32f2f, stop:1 #b71c1c);
            border: none;
            border-radius: 6px;
            color: white;
            font-size: 12px;
            padding: 8px 16px;
        }
        
        #logoutButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e53935, stop:1 #c62828);
        }
        
        #pagesStack {
            background-color: #2d2d2d;
        }
        """
        
        self.setStyleSheet(style)
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        self.closed.emit()
        event.accept()
