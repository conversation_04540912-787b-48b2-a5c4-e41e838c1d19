# -*- coding: utf-8 -*-
"""
الشريط الجانبي
Sidebar Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                            QLabel, QFrame, QScrollArea, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

class SidebarWidget(QWidget):
    """ويدجت الشريط الجانبي"""
    
    # إشارة تغيير الصفحة
    page_changed = pyqtSignal(str)
    
    def __init__(self, user):
        super().__init__()
        self.current_user = user
        self.current_button = None
        self.is_collapsed = False
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setFixedWidth(250)
        self.setObjectName("sidebar")
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # رأس الشريط الجانبي
        header = self.create_header()
        
        # منطقة التنقل
        nav_area = self.create_navigation()
        
        # إضافة العناصر
        main_layout.addWidget(header)
        main_layout.addWidget(nav_area)
    
    def create_header(self):
        """إنشاء رأس الشريط الجانبي"""
        header = QFrame()
        header.setObjectName("sidebarHeader")
        header.setFixedHeight(80)
        
        layout = QVBoxLayout(header)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # شعار الشركة
        logo_label = QLabel("🏭")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setObjectName("logoLabel")
        
        # اسم الشركة
        company_name = QLabel("الحسن ستون")
        company_name.setAlignment(Qt.AlignCenter)
        company_name.setObjectName("companyName")
        
        layout.addWidget(logo_label)
        layout.addWidget(company_name)
        
        return header
    
    def create_navigation(self):
        """إنشاء منطقة التنقل"""
        nav_frame = QFrame()
        nav_frame.setObjectName("navFrame")
        
        layout = QVBoxLayout(nav_frame)
        layout.setContentsMargins(0, 10, 0, 10)
        layout.setSpacing(2)
        
        # قائمة الصفحات
        pages = [
            ("dashboard", "📊", "لوحة التحكم"),
            ("clients", "👥", "العملاء والموردين"),
            ("invoices", "🧾", "فواتير البيع"),
            ("trucks", "🚚", "دخول الجرارات"),
            ("slicing", "🔧", "عمليات النشر"),
            ("inventory", "📦", "إدارة المخزون"),
            ("expenses", "💸", "المصاريف"),
            ("reports", "📈", "التقارير"),
            ("settings", "⚙️", "الإعدادات")
        ]
        
        self.nav_buttons = {}
        
        for page_id, icon, title in pages:
            button = self.create_nav_button(page_id, icon, title)
            self.nav_buttons[page_id] = button
            layout.addWidget(button)
        
        # مساحة فارغة
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        layout.addItem(spacer)
        
        # تعيين الزر الافتراضي
        self.set_active_button("dashboard")
        
        return nav_frame
    
    def create_nav_button(self, page_id: str, icon: str, title: str):
        """إنشاء زر تنقل"""
        button = QPushButton()
        button.setObjectName("navButton")
        button.setFixedHeight(50)
        button.setCursor(Qt.PointingHandCursor)
        
        # تخطيط الزر
        layout = QHBoxLayout(button)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setObjectName("navIcon")
        icon_label.setFixedSize(24, 24)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # النص
        text_label = QLabel(title)
        text_label.setObjectName("navText")
        
        # إضافة العناصر
        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()
        
        # ربط الحدث
        button.clicked.connect(lambda: self.navigate_to_page(page_id))
        
        return button
    
    def navigate_to_page(self, page_id: str):
        """التنقل إلى صفحة"""
        self.set_active_button(page_id)
        self.page_changed.emit(page_id)
    
    def set_active_button(self, page_id: str):
        """تعيين الزر النشط"""
        # إزالة التفعيل من الزر السابق
        if self.current_button:
            self.current_button.setProperty("active", False)
            self.current_button.style().unpolish(self.current_button)
            self.current_button.style().polish(self.current_button)
        
        # تفعيل الزر الجديد
        if page_id in self.nav_buttons:
            self.current_button = self.nav_buttons[page_id]
            self.current_button.setProperty("active", True)
            self.current_button.style().unpolish(self.current_button)
            self.current_button.style().polish(self.current_button)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        #sidebar {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #1a1a1a, stop:1 #2d2d2d);
            border-right: 1px solid #3d3d3d;
        }
        
        #sidebarHeader {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3d3d3d, stop:1 #2d2d2d);
            border-bottom: 1px solid #4d4d4d;
        }
        
        #logoLabel {
            font-size: 24px;
        }
        
        #companyName {
            font-size: 14px;
            font-weight: bold;
            color: #4a9eff;
        }
        
        #navFrame {
            background: transparent;
        }
        
        #navButton {
            background: transparent;
            border: none;
            text-align: left;
            padding: 0px;
            margin: 0px 5px;
            border-radius: 8px;
        }
        
        #navButton:hover {
            background-color: rgba(74, 158, 255, 0.1);
        }
        
        #navButton[active="true"] {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(74, 158, 255, 0.3), stop:1 rgba(74, 158, 255, 0.1));
            border-left: 3px solid #4a9eff;
        }
        
        #navIcon {
            font-size: 18px;
            color: #cccccc;
        }
        
        #navText {
            font-size: 13px;
            color: #cccccc;
            font-weight: 500;
        }
        
        #navButton[active="true"] #navIcon,
        #navButton[active="true"] #navText {
            color: #4a9eff;
        }
        """
        
        self.setStyleSheet(style)
