# -*- coding: utf-8 -*-
"""
لوحة التحكم
Dashboard Widget
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                            QLabel, QFrame, QPushButton, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette

from src.database.database_manager import DatabaseManager

class DashboardWidget(QWidget):
    """ويدجت لوحة التحكم"""
    
    def __init__(self, user):
        super().__init__()
        self.current_user = user
        self.db = DatabaseManager()
        self.setup_ui()
        self.apply_styles()
        self.refresh_data()
        
        # تحديث البيانات كل 30 ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.refresh_data)
        self.timer.start(30000)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        # الويدجت الرئيسي
        main_widget = QWidget()
        scroll_area.setWidget(main_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # رسالة الترحيب
        welcome_section = self.create_welcome_section()
        
        # بطاقات الإحصائيات
        stats_section = self.create_stats_section()
        
        # الأنشطة الأخيرة
        activities_section = self.create_activities_section()
        
        # إضافة الأقسام
        main_layout.addWidget(welcome_section)
        main_layout.addWidget(stats_section)
        main_layout.addWidget(activities_section)
        main_layout.addStretch()
        
        # التخطيط النهائي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(scroll_area)
    
    def create_welcome_section(self):
        """إنشاء قسم الترحيب"""
        section = QFrame()
        section.setObjectName("welcomeSection")
        section.setFixedHeight(100)
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(30, 20, 30, 20)
        
        # رسالة الترحيب
        welcome_label = QLabel(f"مرحباً بك، {self.current_user.full_name}")
        welcome_label.setObjectName("welcomeTitle")
        
        # تاريخ اليوم
        from datetime import datetime
        today = datetime.now().strftime("%A، %d %B %Y")
        date_label = QLabel(f"اليوم: {today}")
        date_label.setObjectName("dateLabel")
        
        layout.addWidget(welcome_label)
        layout.addWidget(date_label)
        
        return section
    
    def create_stats_section(self):
        """إنشاء قسم الإحصائيات"""
        section = QFrame()
        section.setObjectName("statsSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان القسم
        title = QLabel("الإحصائيات السريعة")
        title.setObjectName("sectionTitle")
        
        # شبكة البطاقات
        grid = QGridLayout()
        grid.setSpacing(15)
        
        # بطاقات الإحصائيات
        self.stats_cards = {
            'clients': self.create_stat_card("👥", "العملاء", "0", "#4a9eff"),
            'invoices_today': self.create_stat_card("🧾", "فواتير اليوم", "0", "#66bb6a"),
            'blocks': self.create_stat_card("🧱", "البلوكات", "0", "#ff7043"),
            'slices': self.create_stat_card("📦", "الشرائح الجاهزة", "0", "#ab47bc"),
            'sales_today': self.create_stat_card("💰", "مبيعات اليوم", "0 ج.م", "#ffa726"),
            'expenses_today': self.create_stat_card("💸", "مصاريف اليوم", "0 ج.م", "#ef5350")
        }
        
        # ترتيب البطاقات في الشبكة
        positions = [(0, 0), (0, 1), (0, 2), (1, 0), (1, 1), (1, 2)]
        for i, (key, card) in enumerate(self.stats_cards.items()):
            if i < len(positions):
                row, col = positions[i]
                grid.addWidget(card, row, col)
        
        layout.addWidget(title)
        layout.addLayout(grid)
        
        return section
    
    def create_stat_card(self, icon: str, title: str, value: str, color: str):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("statCard")
        card.setFixedHeight(120)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        # الأيقونة والقيمة
        top_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setObjectName("statIcon")
        icon_label.setStyleSheet(f"color: {color}; font-size: 32px;")
        
        value_label = QLabel(value)
        value_label.setObjectName("statValue")
        value_label.setAlignment(Qt.AlignRight)
        
        top_layout.addWidget(icon_label)
        top_layout.addStretch()
        top_layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setObjectName("statTitle")
        
        layout.addLayout(top_layout)
        layout.addWidget(title_label)
        layout.addStretch()
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
    
    def create_activities_section(self):
        """إنشاء قسم الأنشطة الأخيرة"""
        section = QFrame()
        section.setObjectName("activitiesSection")
        
        layout = QVBoxLayout(section)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان القسم
        title = QLabel("الأنشطة الأخيرة")
        title.setObjectName("sectionTitle")
        
        # قائمة الأنشطة
        self.activities_list = QVBoxLayout()
        
        layout.addWidget(title)
        layout.addLayout(self.activities_list)
        layout.addStretch()
        
        return section
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # تحديث إحصائيات العملاء
            clients_count = self.get_clients_count()
            self.stats_cards['clients'].value_label.setText(str(clients_count))
            
            # تحديث فواتير اليوم
            invoices_today = self.get_invoices_today_count()
            self.stats_cards['invoices_today'].value_label.setText(str(invoices_today))
            
            # تحديث البلوكات
            blocks_count = self.get_blocks_count()
            self.stats_cards['blocks'].value_label.setText(str(blocks_count))
            
            # تحديث الشرائح
            slices_count = self.get_slices_count()
            self.stats_cards['slices'].value_label.setText(str(slices_count))
            
            # تحديث مبيعات اليوم
            sales_today = self.get_sales_today()
            self.stats_cards['sales_today'].value_label.setText(f"{sales_today:,.0f} ج.م")
            
            # تحديث مصاريف اليوم
            expenses_today = self.get_expenses_today()
            self.stats_cards['expenses_today'].value_label.setText(f"{expenses_today:,.0f} ج.م")
            
            # تحديث الأنشطة الأخيرة
            self.refresh_activities()
            
        except Exception as e:
            print(f"خطأ في تحديث البيانات: {e}")
    
    def get_clients_count(self) -> int:
        """الحصول على عدد العملاء"""
        result = self.db.execute_query("SELECT COUNT(*) as count FROM clients")
        return result[0]['count'] if result else 0
    
    def get_invoices_today_count(self) -> int:
        """الحصول على عدد فواتير اليوم"""
        result = self.db.execute_query(
            "SELECT COUNT(*) as count FROM sales_invoices WHERE DATE(invoice_date) = DATE('now')"
        )
        return result[0]['count'] if result else 0
    
    def get_blocks_count(self) -> int:
        """الحصول على عدد البلوكات"""
        result = self.db.execute_query("SELECT COUNT(*) as count FROM blocks WHERE status = 'available'")
        return result[0]['count'] if result else 0
    
    def get_slices_count(self) -> int:
        """الحصول على عدد الشرائح الجاهزة"""
        result = self.db.execute_query("SELECT COUNT(*) as count FROM slices WHERE status = 'available'")
        return result[0]['count'] if result else 0
    
    def get_sales_today(self) -> float:
        """الحصول على مبيعات اليوم"""
        result = self.db.execute_query(
            "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales_invoices WHERE DATE(invoice_date) = DATE('now')"
        )
        return result[0]['total'] if result else 0.0
    
    def get_expenses_today(self) -> float:
        """الحصول على مصاريف اليوم"""
        result = self.db.execute_query(
            "SELECT COALESCE(SUM(amount), 0) as total FROM expenses WHERE DATE(expense_date) = DATE('now')"
        )
        return result[0]['total'] if result else 0.0
    
    def refresh_activities(self):
        """تحديث الأنشطة الأخيرة"""
        # مسح الأنشطة السابقة
        for i in reversed(range(self.activities_list.count())):
            child = self.activities_list.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # إضافة أنشطة تجريبية
        activities = [
            "تم إضافة عميل جديد: أحمد محمد",
            "تم إنشاء فاتورة رقم: INV-001",
            "تم استلام جرار جديد من المورد",
            "تم نشر بلوك رقم: BLK-001"
        ]
        
        for activity in activities:
            activity_widget = self.create_activity_item(activity)
            self.activities_list.addWidget(activity_widget)
    
    def create_activity_item(self, text: str):
        """إنشاء عنصر نشاط"""
        item = QFrame()
        item.setObjectName("activityItem")
        item.setFixedHeight(40)
        
        layout = QHBoxLayout(item)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # نقطة الحالة
        dot = QLabel("●")
        dot.setObjectName("activityDot")
        
        # النص
        text_label = QLabel(text)
        text_label.setObjectName("activityText")
        
        layout.addWidget(dot)
        layout.addWidget(text_label)
        layout.addStretch()
        
        return item
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        #welcomeSection {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #4a9eff, stop:1 #357abd);
            border-radius: 12px;
            color: white;
        }
        
        #welcomeTitle {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }
        
        #dateLabel {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
        }
        
        #statsSection, #activitiesSection {
            background-color: #3d3d3d;
            border-radius: 12px;
            border: 1px solid #4d4d4d;
        }
        
        #sectionTitle {
            font-size: 18px;
            font-weight: bold;
            color: #4a9eff;
            margin-bottom: 10px;
        }
        
        #statCard {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #4d4d4d, stop:1 #3d3d3d);
            border-radius: 10px;
            border: 1px solid #5d5d5d;
        }
        
        #statCard:hover {
            border-color: #4a9eff;
        }
        
        #statValue {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
        }
        
        #statTitle {
            font-size: 12px;
            color: #cccccc;
        }
        
        #activityItem {
            background-color: #4d4d4d;
            border-radius: 6px;
            margin-bottom: 5px;
        }
        
        #activityDot {
            color: #4a9eff;
            font-size: 12px;
        }
        
        #activityText {
            color: #cccccc;
            font-size: 13px;
        }
        """
        
        self.setStyleSheet(style)
