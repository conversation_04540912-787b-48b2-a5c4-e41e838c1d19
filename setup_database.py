#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت إعداد قاعدة البيانات الأولي
Initial Database Setup Script
"""

import os
import sys
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.database_manager import DatabaseManager
from src.models.user import User

def setup_initial_data():
    """إعداد البيانات الأولية"""
    
    print("🔧 إعداد قاعدة البيانات الأولية...")
    
    # تهيئة قاعدة البيانات
    db = DatabaseManager()
    if not db.initialize_database():
        print("❌ فشل في تهيئة قاعدة البيانات")
        return False
    
    print("✅ تم إنشاء قاعدة البيانات بنجاح")
    
    # إضافة مستخدمين إضافيين
    users_data = [
        {
            "username": "manager",
            "password": "manager123",
            "full_name": "مدير المصنع",
            "role": "manager"
        },
        {
            "username": "operator",
            "password": "operator123",
            "full_name": "مشغل النظام",
            "role": "operator"
        },
        {
            "username": "accountant",
            "password": "account123",
            "full_name": "المحاسب",
            "role": "accountant"
        }
    ]
    
    print("👥 إضافة المستخدمين...")
    for user_data in users_data:
        # التحقق من عدم وجود المستخدم
        existing = db.execute_query(
            "SELECT id FROM users WHERE username = ?", 
            (user_data["username"],)
        )
        
        if not existing:
            user = User(
                username=user_data["username"],
                password=user_data["password"],
                full_name=user_data["full_name"],
                role=user_data["role"]
            )
            if user.save():
                print(f"   ✅ تم إضافة المستخدم: {user_data['full_name']}")
            else:
                print(f"   ❌ فشل في إضافة المستخدم: {user_data['full_name']}")
        else:
            print(f"   ⚠️  المستخدم موجود بالفعل: {user_data['full_name']}")
    
    # إضافة عملاء تجريبيين
    print("🏢 إضافة عملاء تجريبيين...")
    clients_data = [
        ("شركة الأهرام للمقاولات", "01234567890", "القاهرة - مصر الجديدة", "<EMAIL>"),
        ("مؤسسة النيل للتشييد", "01098765432", "الجيزة - الدقي", "<EMAIL>"),
        ("شركة الفراعنة للديكور", "01555666777", "الإسكندرية - سموحة", "<EMAIL>"),
        ("مكتب المهندس أحمد محمد", "01444333222", "القاهرة - المعادي", "<EMAIL>"),
        ("شركة الحديثة للرخام", "01777888999", "أسوان - كورنيش النيل", "<EMAIL>")
    ]
    
    for name, phone, address, email in clients_data:
        existing = db.execute_query("SELECT id FROM clients WHERE name = ?", (name,))
        if not existing:
            query = """
            INSERT INTO clients (name, phone, address, email, client_type)
            VALUES (?, ?, ?, ?, 'customer')
            """
            if db.execute_non_query(query, (name, phone, address, email)):
                print(f"   ✅ تم إضافة العميل: {name}")
            else:
                print(f"   ❌ فشل في إضافة العميل: {name}")
        else:
            print(f"   ⚠️  العميل موجود بالفعل: {name}")
    
    # إضافة موردين تجريبيين
    print("🚚 إضافة موردين تجريبيين...")
    suppliers_data = [
        ("محاجر أسوان الحديثة", "01234567890", "أسوان - منطقة المحاجر", "<EMAIL>"),
        ("شركة الصعيد للجرانيت", "01098765432", "قنا - نجع حمادي", "<EMAIL>"),
        ("مؤسسة الجنوب للأحجار", "01555666777", "أسوان - إدفو", "<EMAIL>"),
        ("محاجر الفراعنة", "01444333222", "أسوان - كوم أمبو", "<EMAIL>")
    ]
    
    for name, phone, address, email in suppliers_data:
        existing = db.execute_query("SELECT id FROM suppliers WHERE name = ?", (name,))
        if not existing:
            query = """
            INSERT INTO suppliers (name, phone, address, email)
            VALUES (?, ?, ?, ?)
            """
            if db.execute_non_query(query, (name, phone, address, email)):
                print(f"   ✅ تم إضافة المورد: {name}")
            else:
                print(f"   ❌ فشل في إضافة المورد: {name}")
        else:
            print(f"   ⚠️  المورد موجود بالفعل: {name}")
    
    # إضافة أنواع المصاريف
    print("💸 إضافة أنواع المصاريف...")
    expense_types = [
        ("سولار", "وقود المعدات والآلات"),
        ("صيانة", "صيانة المعدات والآلات"),
        ("رواتب", "رواتب العمال والموظفين"),
        ("نقل", "تكاليف النقل والشحن"),
        ("كهرباء", "فواتير الكهرباء"),
        ("مياه", "فواتير المياه"),
        ("أدوات", "أدوات ومستلزمات العمل"),
        ("تأمينات", "التأمينات الاجتماعية"),
        ("ضرائب", "الضرائب والرسوم"),
        ("أخرى", "مصاريف متنوعة")
    ]
    
    for expense_type, description in expense_types:
        # إضافة مصروف تجريبي
        query = """
        INSERT INTO expenses (expense_type, description, amount, expense_date, category, created_by)
        VALUES (?, ?, ?, DATE('now'), 'تشغيلية', 1)
        """
        amount = 1000.0  # مبلغ تجريبي
        if db.execute_non_query(query, (expense_type, description, amount)):
            print(f"   ✅ تم إضافة مصروف: {expense_type}")
    
    print("\n🎉 تم إعداد البيانات الأولية بنجاح!")
    print("\n📊 ملخص البيانات المُضافة:")
    
    # عرض إحصائيات
    stats = {
        "المستخدمين": db.execute_query("SELECT COUNT(*) as count FROM users")[0]['count'],
        "العملاء": db.execute_query("SELECT COUNT(*) as count FROM clients")[0]['count'],
        "الموردين": db.execute_query("SELECT COUNT(*) as count FROM suppliers")[0]['count'],
        "المصاريف": db.execute_query("SELECT COUNT(*) as count FROM expenses")[0]['count']
    }
    
    for category, count in stats.items():
        print(f"   📈 {category}: {count}")
    
    print("\n🔐 بيانات الدخول المتاحة:")
    print("   👤 المدير العام: admin / 123456")
    print("   👤 مدير المصنع: manager / manager123")
    print("   👤 مشغل النظام: operator / operator123")
    print("   👤 المحاسب: accountant / account123")
    
    return True

def reset_database():
    """إعادة تعيين قاعدة البيانات"""
    
    print("⚠️  تحذير: سيتم حذف جميع البيانات الموجودة!")
    response = input("هل تريد المتابعة؟ (y/N): ")
    
    if response.lower() != 'y':
        print("تم إلغاء العملية")
        return False
    
    # حذف ملف قاعدة البيانات
    db_path = "data/alhassan_stone.db"
    if os.path.exists(db_path):
        os.remove(db_path)
        print("🗑️  تم حذف قاعدة البيانات القديمة")
    
    # إعادة إنشاء قاعدة البيانات
    return setup_initial_data()

def main():
    """الدالة الرئيسية"""
    
    print("=" * 60)
    print("🏭 إعداد قاعدة بيانات مصنع الحسن ستون")
    print("   Al-Hassan Stone Database Setup")
    print("=" * 60)
    print()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        # إعادة تعيين قاعدة البيانات
        reset_database()
    else:
        # إعداد عادي
        setup_initial_data()

if __name__ == "__main__":
    main()
