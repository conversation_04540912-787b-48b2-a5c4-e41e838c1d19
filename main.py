#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة مصنع الجرانيت - الحسن ستون
Al-Hassan Stone Granite Factory Management System

الملف الرئيسي لتشغيل البرنامج
Main application entry point
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTranslator, QLocale
from PyQt5.QtGui import QFont, QIcon

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.login_window import LoginWindow
from src.database.database_manager import DatabaseManager
from src.utils.config import Config

def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)

    # إعداد اللغة العربية والاتجاه من اليمين لليسار
    app.setLayoutDirection(Qt.RightToLeft)

    # إعداد الخط العربي
    font = QFont("Arial", 10)
    font.setFamily("Segoe UI")
    app.setFont(font)

    # تطبيق الأنماط
    try:
        with open("assets/styles/main_style.qss", "r", encoding="utf-8") as f:
            app.setStyleSheet(f.read())
    except FileNotFoundError:
        print("تحذير: لم يتم العثور على ملف الأنماط")

    # إعداد أيقونة التطبيق
    if os.path.exists("assets/icons/app_icon.png"):
        app.setWindowIcon(QIcon("assets/icons/app_icon.png"))

    # إعداد معلومات التطبيق
    app.setApplicationName("الحسن ستون")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Al-Hassan Stone")

    return app

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # إنشاء التطبيق
        app = setup_application()
        
        # تهيئة قاعدة البيانات
        db_manager = DatabaseManager()
        if not db_manager.initialize_database():
            print("خطأ في تهيئة قاعدة البيانات")
            return 1
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow()
        login_window.show()
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
