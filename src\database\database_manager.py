# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager
"""

import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from src.utils.config import app_config

class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = app_config.get('database.path', 'data/alhassan_stone.db')
        self.connection = None
        self.ensure_data_directory()
    
    def ensure_data_directory(self):
        """التأكد من وجود مجلد البيانات"""
        data_dir = os.path.dirname(self.db_path)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
    
    def connect(self) -> bool:
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> Optional[List[Dict]]:
        """تنفيذ استعلام SELECT وإرجاع النتائج"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            
            # تحويل النتائج إلى قائمة من القواميس
            columns = [description[0] for description in cursor.description]
            results = []
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
            
            return results
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
    
    def execute_non_query(self, query: str, params: tuple = ()) -> bool:
        """تنفيذ استعلام INSERT/UPDATE/DELETE"""
        try:
            if not self.connection:
                self.connect()
            
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return True
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def get_last_insert_id(self) -> int:
        """الحصول على آخر ID تم إدراجه"""
        if self.connection:
            return self.connection.lastrowid
        return 0
    
    def initialize_database(self) -> bool:
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            if not self.connect():
                return False
            
            # إنشاء الجداول
            self._create_tables()
            
            # إدراج البيانات الأولية
            self._insert_initial_data()
            
            return True
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            return False
    
    def _create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        """
        
        # جدول العملاء
        clients_table = """
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            email TEXT,
            client_type TEXT DEFAULT 'customer',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الموردين
        suppliers_table = """
        CREATE TABLE IF NOT EXISTS suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            address TEXT,
            email TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول دخول الجرارات
        truck_entries_table = """
        CREATE TABLE IF NOT EXISTS truck_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            supplier_id INTEGER,
            truck_number TEXT,
            blocks_count INTEGER NOT NULL,
            weight_tons REAL NOT NULL,
            price_per_ton REAL NOT NULL,
            truck_cost REAL DEFAULT 0,
            total_cost REAL NOT NULL,
            entry_date DATE DEFAULT CURRENT_DATE,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
        )
        """
        
        # جدول البلوكات
        blocks_table = """
        CREATE TABLE IF NOT EXISTS blocks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            truck_entry_id INTEGER,
            block_number TEXT UNIQUE NOT NULL,
            weight_tons REAL,
            granite_type TEXT,
            status TEXT DEFAULT 'available',
            location TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (truck_entry_id) REFERENCES truck_entries (id)
        )
        """
        
        # جدول عمليات النشر
        slicing_operations_table = """
        CREATE TABLE IF NOT EXISTS slicing_operations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            block_id INTEGER,
            slices_count INTEGER NOT NULL,
            waste_percentage REAL DEFAULT 0,
            slicing_cost REAL DEFAULT 0,
            operation_date DATE DEFAULT CURRENT_DATE,
            operator_name TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (block_id) REFERENCES blocks (id)
        )
        """
        
        # جدول الشرائح
        slices_table = """
        CREATE TABLE IF NOT EXISTS slices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            slicing_operation_id INTEGER,
            slice_number TEXT NOT NULL,
            length_cm REAL,
            width_cm REAL,
            thickness_cm REAL,
            area_sqm REAL,
            granite_type TEXT,
            quality_grade TEXT DEFAULT 'A',
            status TEXT DEFAULT 'available',
            location TEXT,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (slicing_operation_id) REFERENCES slicing_operations (id)
        )
        """
        
        # جدول فواتير البيع
        sales_invoices_table = """
        CREATE TABLE IF NOT EXISTS sales_invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            client_id INTEGER,
            invoice_date DATE DEFAULT CURRENT_DATE,
            subtotal REAL NOT NULL,
            discount_percentage REAL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            tax_percentage REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            paid_amount REAL DEFAULT 0,
            remaining_amount REAL DEFAULT 0,
            payment_status TEXT DEFAULT 'pending',
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """
        
        # جدول عناصر الفاتورة
        invoice_items_table = """
        CREATE TABLE IF NOT EXISTS invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            slice_id INTEGER,
            description TEXT,
            length_cm REAL,
            width_cm REAL,
            thickness_cm REAL,
            area_sqm REAL,
            quantity INTEGER DEFAULT 1,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
            FOREIGN KEY (slice_id) REFERENCES slices (id)
        )
        """
        
        # جدول المصاريف
        expenses_table = """
        CREATE TABLE IF NOT EXISTS expenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            expense_type TEXT NOT NULL,
            description TEXT,
            amount REAL NOT NULL,
            expense_date DATE DEFAULT CURRENT_DATE,
            category TEXT,
            receipt_number TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """
        
        # جدول المدفوعات
        payments_table = """
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER,
            client_id INTEGER,
            amount REAL NOT NULL,
            payment_method TEXT DEFAULT 'cash',
            payment_date DATE DEFAULT CURRENT_DATE,
            reference_number TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """
        
        # جدول سجل النشاطات
        activity_logs_table = """
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            action TEXT NOT NULL,
            table_name TEXT,
            record_id INTEGER,
            old_values TEXT,
            new_values TEXT,
            ip_address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
        """
        
        # تنفيذ إنشاء الجداول
        tables = [
            users_table, clients_table, suppliers_table, truck_entries_table,
            blocks_table, slicing_operations_table, slices_table,
            sales_invoices_table, invoice_items_table, expenses_table,
            payments_table, activity_logs_table
        ]
        
        for table_sql in tables:
            self.execute_non_query(table_sql)
    
    def _insert_initial_data(self):
        """إدراج البيانات الأولية"""
        
        # إنشاء مستخدم افتراضي (admin)
        admin_exists = self.execute_query(
            "SELECT id FROM users WHERE username = ?", ("admin",)
        )
        
        if not admin_exists:
            self.execute_non_query(
                """INSERT INTO users (username, password, full_name, role) 
                   VALUES (?, ?, ?, ?)""",
                ("admin", "123456", "المدير العام", "admin")
            )
        
        # إضافة أنواع المصاريف الافتراضية
        expense_types = [
            "سولار", "صيانة", "رواتب", "نقل", "كهرباء", "مياه", "أدوات", "أخرى"
        ]
        
        # إضافة أنواع الجرانيت الافتراضية
        granite_types = [
            "جرانيت أحمر أسوان", "جرانيت رمادي", "جرانيت أسود", "جرانيت وردي", "جرانيت أبيض"
        ]
