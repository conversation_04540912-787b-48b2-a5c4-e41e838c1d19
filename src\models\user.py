# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model
"""

from datetime import datetime
from typing import Optional, List, Dict
from src.database.database_manager import DatabaseManager

class User:
    """فئة المستخدم"""
    
    def __init__(self, user_id: int = None, username: str = "", password: str = "",
                 full_name: str = "", role: str = "user", is_active: bool = True):
        self.id = user_id
        self.username = username
        self.password = password
        self.full_name = full_name
        self.role = role
        self.is_active = is_active
        self.created_at = None
        self.last_login = None
        self.db = DatabaseManager()
    
    def save(self) -> bool:
        """حفظ المستخدم في قاعدة البيانات"""
        try:
            if self.id:
                # تحديث مستخدم موجود
                query = """
                UPDATE users 
                SET username=?, password=?, full_name=?, role=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
                """
                params = (self.username, self.password, self.full_name, self.role, self.is_active, self.id)
            else:
                # إنشاء مستخدم جديد
                query = """
                INSERT INTO users (username, password, full_name, role, is_active)
                VALUES (?, ?, ?, ?, ?)
                """
                params = (self.username, self.password, self.full_name, self.role, self.is_active)
            
            if self.db.execute_non_query(query, params):
                if not self.id:
                    self.id = self.db.get_last_insert_id()
                return True
            return False
        except Exception as e:
            print(f"خطأ في حفظ المستخدم: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف المستخدم"""
        if not self.id:
            return False
        
        query = "DELETE FROM users WHERE id=?"
        return self.db.execute_non_query(query, (self.id,))
    
    def update_last_login(self) -> bool:
        """تحديث آخر تسجيل دخول"""
        if not self.id:
            return False
        
        query = "UPDATE users SET last_login=CURRENT_TIMESTAMP WHERE id=?"
        return self.db.execute_non_query(query, (self.id,))
    
    @classmethod
    def authenticate(cls, username: str, password: str) -> Optional['User']:
        """التحقق من صحة بيانات المستخدم"""
        db = DatabaseManager()
        query = """
        SELECT id, username, password, full_name, role, is_active, created_at, last_login
        FROM users 
        WHERE username=? AND password=? AND is_active=1
        """
        
        result = db.execute_query(query, (username, password))
        if result and len(result) > 0:
            user_data = result[0]
            user = cls(
                user_id=user_data['id'],
                username=user_data['username'],
                password=user_data['password'],
                full_name=user_data['full_name'],
                role=user_data['role'],
                is_active=bool(user_data['is_active'])
            )
            user.created_at = user_data['created_at']
            user.last_login = user_data['last_login']
            return user
        
        return None
    
    @classmethod
    def get_by_id(cls, user_id: int) -> Optional['User']:
        """الحصول على مستخدم بواسطة المعرف"""
        db = DatabaseManager()
        query = """
        SELECT id, username, password, full_name, role, is_active, created_at, last_login
        FROM users WHERE id=?
        """
        
        result = db.execute_query(query, (user_id,))
        if result and len(result) > 0:
            user_data = result[0]
            user = cls(
                user_id=user_data['id'],
                username=user_data['username'],
                password=user_data['password'],
                full_name=user_data['full_name'],
                role=user_data['role'],
                is_active=bool(user_data['is_active'])
            )
            user.created_at = user_data['created_at']
            user.last_login = user_data['last_login']
            return user
        
        return None
    
    @classmethod
    def get_all(cls) -> List['User']:
        """الحصول على جميع المستخدمين"""
        db = DatabaseManager()
        query = """
        SELECT id, username, password, full_name, role, is_active, created_at, last_login
        FROM users ORDER BY full_name
        """
        
        result = db.execute_query(query)
        users = []
        
        if result:
            for user_data in result:
                user = cls(
                    user_id=user_data['id'],
                    username=user_data['username'],
                    password=user_data['password'],
                    full_name=user_data['full_name'],
                    role=user_data['role'],
                    is_active=bool(user_data['is_active'])
                )
                user.created_at = user_data['created_at']
                user.last_login = user_data['last_login']
                users.append(user)
        
        return users
    
    def to_dict(self) -> Dict:
        """تحويل المستخدم إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'last_login': self.last_login
        }
    
    def __str__(self):
        return f"{self.full_name} ({self.username})"
