# دليل المطور - برنامج الحسن ستون
## Developer Guide - Al-Hassan Stone Management System

### 🎯 الحالة الحالية للمشروع

#### ✅ المكتمل (المرحلة الأولى)
- **هيكل المشروع الأساسي**: تم إنشاء جميع المجلدات والملفات الأساسية
- **قاعدة البيانات SQLite**: تصميم شامل مع 12 جدول رئيسي
- **نظام تسجيل الدخول**: واجهة أنيقة مع التحقق من المستخدمين
- **الشاشة الرئيسية**: لوحة تحكم مع إحصائيات وشريط جانبي
- **التصميم العصري**: أنماط QSS داكنة مع دعم RTL للعربية
- **نظام الإعدادات**: ملف JSON للتكوين
- **أدوات التطوير**: سكريبتات الاختبار والبناء

#### 🔄 قيد التطوير (المرحلة الثانية)
- إدارة العملاء والموردين
- نظام فواتير البيع
- تسجيل دخول الجرارات
- عمليات النشر
- إدارة المخزون
- المصاريف التشغيلية
- التقارير والإحصائيات
- الإعدادات المتقدمة

### 🏗️ هيكل المشروع

```
alhassan-stone/
├── 📄 main.py                 # نقطة الدخول الرئيسية
├── 🚀 run.py                  # ملف تشغيل مبسط
├── 🔧 setup_database.py       # إعداد قاعدة البيانات
├── 🧪 test_app.py             # اختبارات التطبيق
├── 🏗️ build_exe.py            # بناء ملف تنفيذي
├── 🖥️ start.bat              # تشغيل على Windows
├── 📋 requirements.txt        # متطلبات Python
├── ⚙️ config.json            # ملف الإعدادات
├── 📖 README.md              # دليل المستخدم
├── 👨‍💻 DEVELOPER_GUIDE.md     # دليل المطور
│
├── 📁 src/                   # الكود المصدري
│   ├── 🖼️ ui/                # واجهات المستخدم
│   │   ├── login_window.py   # نافذة تسجيل الدخول
│   │   ├── main_window.py    # النافذة الرئيسية
│   │   ├── sidebar.py        # الشريط الجانبي
│   │   └── dashboard.py      # لوحة التحكم
│   │
│   ├── 🗄️ database/          # قاعدة البيانات
│   │   └── database_manager.py
│   │
│   ├── 📊 models/            # نماذج البيانات
│   │   └── user.py           # نموذج المستخدم
│   │
│   └── 🛠️ utils/             # أدوات مساعدة
│       └── config.py         # إدارة الإعدادات
│
├── 📁 assets/                # الموارد
│   ├── 🎨 styles/            # ملفات الأنماط
│   │   └── main_style.qss
│   ├── 🖼️ icons/             # الأيقونات
│   └── 🔤 fonts/             # الخطوط
│
├── 📁 data/                  # قاعدة البيانات
│   └── alhassan_stone.db
│
├── 📁 reports/               # التقارير المُصدرة
└── 📁 backups/               # النسخ الاحتياطية
```

### 🗄️ قاعدة البيانات

#### الجداول الرئيسية:
1. **users** - المستخدمين والصلاحيات
2. **clients** - العملاء
3. **suppliers** - الموردين
4. **truck_entries** - دخول الجرارات
5. **blocks** - البلوكات
6. **slicing_operations** - عمليات النشر
7. **slices** - الشرائح
8. **sales_invoices** - فواتير البيع
9. **invoice_items** - عناصر الفاتورة
10. **expenses** - المصاريف
11. **payments** - المدفوعات
12. **activity_logs** - سجل الأنشطة

### 🎨 نظام التصميم

#### الألوان الرئيسية:
- **الخلفية الرئيسية**: `#1e1e1e`
- **الخلفية الثانوية**: `#2d2d2d`
- **الحدود**: `#3d3d3d` / `#4d4d4d`
- **اللون الأساسي**: `#4a9eff` (أزرق)
- **النص الرئيسي**: `#ffffff`
- **النص الثانوي**: `#cccccc`

#### الخطوط:
- **الخط الأساسي**: Segoe UI
- **الحجم الافتراضي**: 10-14px
- **دعم العربية**: RTL Layout

### 🚀 كيفية التشغيل

#### الطريقة الأولى (مبسطة):
```bash
# على Windows
start.bat

# على Linux/Mac
python run.py
```

#### الطريقة الثانية (يدوية):
```bash
# 1. تثبيت المتطلبات
pip install -r requirements.txt

# 2. إعداد قاعدة البيانات
python setup_database.py

# 3. تشغيل البرنامج
python main.py
```

### 🧪 الاختبار

```bash
# تشغيل اختبارات شاملة
python test_app.py

# إعادة تعيين قاعدة البيانات
python setup_database.py --reset
```

### 🏗️ بناء ملف تنفيذي

```bash
# بناء ملف EXE
python build_exe.py

# الملف سيكون في: dist/AlHassanStone.exe
```

### 👤 بيانات الدخول الافتراضية

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin | 123456 | مدير عام |
| manager | manager123 | مدير مصنع |
| operator | operator123 | مشغل |
| accountant | account123 | محاسب |

### 🔧 إضافة وظائف جديدة

#### 1. إنشاء نموذج جديد:
```python
# src/models/new_model.py
class NewModel:
    def __init__(self):
        self.db = DatabaseManager()
    
    def save(self):
        # منطق الحفظ
        pass
```

#### 2. إنشاء واجهة جديدة:
```python
# src/ui/new_widget.py
from PyQt5.QtWidgets import QWidget

class NewWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        # تصميم الواجهة
        pass
```

#### 3. إضافة الواجهة للشريط الجانبي:
```python
# في src/ui/sidebar.py
pages = [
    # الصفحات الموجودة...
    ("new_page", "🆕", "الصفحة الجديدة"),
]
```

#### 4. ربط الواجهة بالنافذة الرئيسية:
```python
# في src/ui/main_window.py
def setup_pages(self):
    # الصفحات الموجودة...
    self.new_page = NewWidget()
    self.pages_stack.addWidget(self.new_page)
```

### 📊 إضافة تقارير جديدة

```python
# src/reports/new_report.py
from reportlab.pdfgen import canvas

class NewReport:
    def generate_pdf(self, data):
        # منطق إنشاء PDF
        pass
    
    def generate_excel(self, data):
        # منطق إنشاء Excel
        pass
```

### 🎨 تخصيص الأنماط

```css
/* في assets/styles/main_style.qss */
#newWidget {
    background-color: #2d2d2d;
    border-radius: 8px;
    border: 1px solid #4d4d4d;
}

#newButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4a9eff, stop:1 #357abd);
    border-radius: 6px;
    color: white;
    padding: 8px 16px;
}
```

### 🔒 إدارة الصلاحيات

```python
# في src/models/user.py
def has_permission(self, permission):
    permissions = {
        'admin': ['all'],
        'manager': ['view', 'edit', 'delete'],
        'operator': ['view', 'edit'],
        'accountant': ['view', 'reports']
    }
    return permission in permissions.get(self.role, [])
```

### 📝 إضافة ترجمات

```python
# src/utils/translations.py
translations = {
    'ar': {
        'welcome': 'مرحباً',
        'login': 'تسجيل الدخول',
        # ...
    },
    'en': {
        'welcome': 'Welcome',
        'login': 'Login',
        # ...
    }
}
```

### 🐛 تتبع الأخطاء

```python
# src/utils/logger.py
import logging

def setup_logger():
    logging.basicConfig(
        filename='logs/app.log',
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
```

### 📈 خطة التطوير المستقبلية

#### المرحلة الثانية (الحالية):
- [ ] إدارة العملاء والموردين
- [ ] نظام فواتير البيع
- [ ] تسجيل دخول الجرارات
- [ ] عمليات النشر

#### المرحلة الثالثة:
- [ ] إدارة المخزون المتقدمة
- [ ] التقارير والإحصائيات
- [ ] النسخ الاحتياطي التلقائي
- [ ] نظام التنبيهات

#### المرحلة الرابعة:
- [ ] واجهة ويب
- [ ] تطبيق موبايل
- [ ] التكامل مع أنظمة خارجية
- [ ] الذكاء الاصطناعي للتنبؤات

### 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/new-feature`)
3. Commit التغييرات (`git commit -am 'Add new feature'`)
4. Push للـ branch (`git push origin feature/new-feature`)
5. إنشاء Pull Request

### 📞 الدعم

للحصول على المساعدة:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير
- مراجعة الوثائق

---
**تم تطوير هذا النظام بعناية فائقة لخدمة مصنع الحسن ستون للجرانيت**
