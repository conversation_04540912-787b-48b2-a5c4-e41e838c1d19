# برنامج إدارة مصنع الجرانيت - الحسن ستون
## Al-Hassan Stone Granite Factory Management System

### 📋 وصف المشروع
برنامج شامل لإدارة مصنع الجرانيت يشمل جميع العمليات من استلام البلوكات وحتى بيع الشرائح النهائية، مع نظام محاسبي متكامل وتقارير مفصلة.

### 🚀 المميزات الرئيسية
- **واجهة عربية حديثة**: تصميم داكن أنيق مع دعم كامل للغة العربية واتجاه RTL
- **إدارة العملاء والموردين**: نظام شامل لإدارة جهات الاتصال
- **تتبع البلوكات والشرائح**: من الاستلام وحتى البيع
- **نظام الفواتير**: إنشاء وطباعة الفواتير مع حسابات تلقائية
- **إدارة المخزون**: تتبع المخزون مع تنبيهات النفاد
- **التقارير والإحصائيات**: تقارير مفصلة مع رسوم بيانية
- **النسخ الاحتياطي**: حماية البيانات مع إمكانية الاستعادة

### 🛠️ التقنيات المستخدمة
- **Python 3.8+**
- **PyQt5** - واجهة المستخدم الرسومية
- **SQLite** - قاعدة البيانات المحلية
- **ReportLab** - إنشاء تقارير PDF
- **Matplotlib** - الرسوم البيانية
- **Pandas** - معالجة البيانات

### 📦 متطلبات التشغيل
```bash
Python 3.8 أو أحدث
PyQt5 5.15+
SQLite3 (مدمج مع Python)
```

### 🔧 التثبيت والتشغيل

#### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/alhassan-stone.git
cd alhassan-stone
```

#### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 3. تشغيل البرنامج
```bash
python main.py
```

### 👤 بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: 123456

### 📁 هيكل المشروع
```
alhassan-stone/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # متطلبات Python
├── README.md              # ملف التوثيق
├── config.json            # ملف الإعدادات
├── src/                   # الكود المصدري
│   ├── __init__.py
│   ├── ui/                # واجهات المستخدم
│   │   ├── login_window.py
│   │   ├── main_window.py
│   │   ├── sidebar.py
│   │   └── dashboard.py
│   ├── database/          # قاعدة البيانات
│   │   ├── database_manager.py
│   │   └── __init__.py
│   ├── models/            # نماذج البيانات
│   │   ├── user.py
│   │   └── __init__.py
│   └── utils/             # أدوات مساعدة
│       ├── config.py
│       └── __init__.py
├── assets/                # الموارد
│   ├── icons/             # الأيقونات
│   ├── styles/            # ملفات الأنماط
│   │   └── main_style.qss
│   └── fonts/             # الخطوط
├── data/                  # قاعدة البيانات
│   └── alhassan_stone.db
├── reports/               # التقارير المُصدرة
└── backups/               # النسخ الاحتياطية
```

### 🎯 الوظائف المتاحة حالياً
- ✅ نظام تسجيل الدخول
- ✅ لوحة التحكم الرئيسية
- ✅ قاعدة البيانات الأساسية
- ✅ التصميم العصري والأنماط
- 🔄 إدارة العملاء والموردين (قيد التطوير)
- 🔄 نظام الفواتير (قيد التطوير)
- 🔄 إدارة المخزون (قيد التطوير)
- 🔄 التقارير (قيد التطوير)

### 🚧 الوظائف قيد التطوير
- إدارة العملاء والموردين
- نظام فواتير البيع
- تسجيل دخول الجرارات
- عمليات النشر
- إدارة المخزون
- المصاريف التشغيلية
- التقارير والإحصائيات
- الإعدادات المتقدمة

### 📊 قاعدة البيانات
يستخدم البرنامج قاعدة بيانات SQLite مع الجداول التالية:
- `users` - المستخدمين
- `clients` - العملاء
- `suppliers` - الموردين
- `truck_entries` - دخول الجرارات
- `blocks` - البلوكات
- `slicing_operations` - عمليات النشر
- `slices` - الشرائح
- `sales_invoices` - فواتير البيع
- `invoice_items` - عناصر الفاتورة
- `expenses` - المصاريف
- `payments` - المدفوعات
- `activity_logs` - سجل الأنشطة

### 🔒 الأمان
- تشفير كلمات المرور
- تسجيل جميع العمليات
- نظام صلاحيات المستخدمين
- النسخ الاحتياطي التلقائي

### 🆘 الدعم والمساعدة
للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

### 📝 الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

### 🤝 المساهمة
نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل إرسال Pull Request.

### 📈 خطة التطوير
- **المرحلة 1**: الوظائف الأساسية (مكتملة)
- **المرحلة 2**: إدارة العملاء والفواتير (قيد التطوير)
- **المرحلة 3**: التقارير المتقدمة
- **المرحلة 4**: التكامل مع أنظمة خارجية

---
**تم تطوير هذا البرنامج خصيصاً لمصنع الحسن ستون للجرانيت**
