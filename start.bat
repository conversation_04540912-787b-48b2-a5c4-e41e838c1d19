@echo off
chcp 65001 >nul
title الحسن ستون - Al-<PERSON> Stone

echo.
echo ===============================================
echo 🏭 مصنع الحسن ستون للجرانيت
echo    Al-Hassan Stone Granite Factory
echo ===============================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متاح
    pause
    exit /b 1
)

echo ✅ pip متاح

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo 📦 تثبيت المتطلبات...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

REM إعداد قاعدة البيانات إذا لم تكن موجودة
if not exist "data\alhassan_stone.db" (
    echo 🔧 إعداد قاعدة البيانات الأولية...
    python setup_database.py
    if errorlevel 1 (
        echo ❌ فشل في إعداد قاعدة البيانات
        pause
        exit /b 1
    )
    echo ✅ تم إعداد قاعدة البيانات
)

echo.
echo 🚀 تشغيل البرنامج...
echo.

REM تشغيل البرنامج
python run.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق البرنامج بنجاح
pause
