/* 
الأنماط الرئيسية لبرنامج الحسن ستون
Main Styles for Al-Hassan Stone Application
*/

/* الإعدادات العامة */
* {
    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
    outline: none;
}

QWidget {
    background-color: #1e1e1e;
    color: #ffffff;
    selection-background-color: #4a9eff;
    selection-color: #ffffff;
}

/* الخطوط */
QLabel {
    color: #ffffff;
}

/* الأزرار */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4a9eff, stop:1 #357abd);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    padding: 8px 16px;
    min-width: 80px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #5ba6ff, stop:1 #4285d1);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3d8bef, stop:1 #2a6ba3);
}

QPushButton:disabled {
    background-color: #3d3d3d;
    color: #666666;
}

/* حقول الإدخال */
QLineEdit {
    background-color: #2d2d2d;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    padding: 8px 12px;
    min-height: 16px;
}

QLineEdit:focus {
    border-color: #4a9eff;
    background-color: #333333;
}

QLineEdit:disabled {
    background-color: #1a1a1a;
    color: #666666;
    border-color: #2d2d2d;
}

/* مربعات النص */
QTextEdit, QPlainTextEdit {
    background-color: #2d2d2d;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    padding: 8px;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #4a9eff;
    background-color: #333333;
}

/* القوائم المنسدلة */
QComboBox {
    background-color: #2d2d2d;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    padding: 8px 12px;
    min-height: 16px;
}

QComboBox:focus {
    border-color: #4a9eff;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(assets/icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 4px;
    color: #ffffff;
    selection-background-color: #4a9eff;
}

/* خانات الاختيار */
QCheckBox {
    color: #ffffff;
    font-size: 13px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
}

QCheckBox::indicator:unchecked {
    border: 2px solid #3d3d3d;
    background-color: #2d2d2d;
    border-radius: 3px;
}

QCheckBox::indicator:checked {
    border: 2px solid #4a9eff;
    background-color: #4a9eff;
    border-radius: 3px;
    image: url(assets/icons/check.png);
}

QCheckBox::indicator:hover {
    border-color: #5ba6ff;
}

/* أزرار الراديو */
QRadioButton {
    color: #ffffff;
    font-size: 13px;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
}

QRadioButton::indicator:unchecked {
    border: 2px solid #3d3d3d;
    background-color: #2d2d2d;
    border-radius: 9px;
}

QRadioButton::indicator:checked {
    border: 2px solid #4a9eff;
    background-color: #4a9eff;
    border-radius: 9px;
}

/* الجداول */
QTableWidget, QTableView {
    background-color: #2d2d2d;
    alternate-background-color: #333333;
    border: 1px solid #4d4d4d;
    border-radius: 6px;
    color: #ffffff;
    gridline-color: #4d4d4d;
    selection-background-color: #4a9eff;
}

QTableWidget::item, QTableView::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected, QTableView::item:selected {
    background-color: #4a9eff;
    color: #ffffff;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4d4d4d, stop:1 #3d3d3d);
    border: 1px solid #5d5d5d;
    color: #ffffff;
    font-weight: bold;
    padding: 8px;
}

QHeaderView::section:hover {
    background-color: #5d5d5d;
}

/* أشرطة التمرير */
QScrollBar:vertical {
    background-color: #2d2d2d;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #4d4d4d;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #5d5d5d;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #2d2d2d;
    height: 12px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal {
    background-color: #4d4d4d;
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #5d5d5d;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* علامات التبويب */
QTabWidget::pane {
    border: 1px solid #4d4d4d;
    border-radius: 6px;
    background-color: #2d2d2d;
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #3d3d3d, stop:1 #2d2d2d);
    border: 1px solid #4d4d4d;
    color: #cccccc;
    padding: 8px 16px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4a9eff, stop:1 #357abd);
    color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #4d4d4d;
}

/* القوائم */
QMenuBar {
    background-color: #2d2d2d;
    border-bottom: 1px solid #4d4d4d;
    color: #ffffff;
}

QMenuBar::item {
    background: transparent;
    padding: 8px 12px;
}

QMenuBar::item:selected {
    background-color: #4a9eff;
}

QMenu {
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 4px;
    color: #ffffff;
}

QMenu::item {
    padding: 8px 20px;
}

QMenu::item:selected {
    background-color: #4a9eff;
}

/* شريط الحالة */
QStatusBar {
    background-color: #2d2d2d;
    border-top: 1px solid #4d4d4d;
    color: #cccccc;
}

/* مربعات الحوار */
QMessageBox {
    background-color: #2d2d2d;
    color: #ffffff;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 6px 12px;
}

/* شريط التقدم */
QProgressBar {
    background-color: #2d2d2d;
    border: 1px solid #4d4d4d;
    border-radius: 6px;
    text-align: center;
    color: #ffffff;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #4a9eff, stop:1 #357abd);
    border-radius: 5px;
}

/* المنزلقات */
QSlider::groove:horizontal {
    background-color: #3d3d3d;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #4a9eff;
    width: 18px;
    height: 18px;
    border-radius: 9px;
    margin: -6px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #5ba6ff;
}

/* الإطارات */
QFrame {
    border: none;
}

QFrame[frameShape="1"] { /* Box */
    border: 1px solid #4d4d4d;
    border-radius: 6px;
}

QFrame[frameShape="2"] { /* Panel */
    border: 2px solid #4d4d4d;
    border-radius: 6px;
}

/* أدوات التاريخ والوقت */
QDateEdit, QTimeEdit, QDateTimeEdit {
    background-color: #2d2d2d;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    padding: 8px 12px;
}

QDateEdit:focus, QTimeEdit:focus, QDateTimeEdit:focus {
    border-color: #4a9eff;
}

QCalendarWidget {
    background-color: #2d2d2d;
    color: #ffffff;
}

QCalendarWidget QToolButton {
    background-color: #3d3d3d;
    color: #ffffff;
    border: 1px solid #4d4d4d;
    border-radius: 4px;
}

QCalendarWidget QToolButton:hover {
    background-color: #4a9eff;
}

/* أدوات الأرقام */
QSpinBox, QDoubleSpinBox {
    background-color: #2d2d2d;
    border: 2px solid #3d3d3d;
    border-radius: 6px;
    color: #ffffff;
    font-size: 13px;
    padding: 8px 12px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border-color: #4a9eff;
}

QSpinBox::up-button, QDoubleSpinBox::up-button {
    background-color: #3d3d3d;
    border: none;
    border-radius: 3px;
    width: 16px;
}

QSpinBox::down-button, QDoubleSpinBox::down-button {
    background-color: #3d3d3d;
    border: none;
    border-radius: 3px;
    width: 16px;
}
