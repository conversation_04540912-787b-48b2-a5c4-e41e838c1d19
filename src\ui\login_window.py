# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window
"""

import sys
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QMessageBox, QFrame,
                            QApplication, QCheckBox, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QBrush, QIcon

from src.models.user import User
from src.ui.main_window import MainWindow

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    # إشارة نجاح تسجيل الدخول
    login_successful = pyqtSignal(object)
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        self.main_window = None
        self.setup_ui()
        self.apply_styles()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الحسن ستون - تسجيل الدخول")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.FramelessWindowHint)
        
        # تخطيط رئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setObjectName("loginFrame")
        
        # تخطيط إطار تسجيل الدخول
        login_layout = QVBoxLayout(login_frame)
        login_layout.setContentsMargins(40, 40, 40, 40)
        login_layout.setSpacing(20)
        
        # شعار الشركة
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setFixedHeight(80)
        logo_label.setText("🏭")  # يمكن استبداله بصورة الشعار
        logo_label.setStyleSheet("font-size: 48px;")
        
        # عنوان الشركة
        title_label = QLabel("مصنع الحسن ستون للجرانيت")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        
        # عنوان فرعي
        subtitle_label = QLabel("نظام إدارة المصنع")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setObjectName("subtitleLabel")
        
        # مساحة فارغة
        spacer1 = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("fieldLabel")
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setObjectName("inputField")
        self.username_input.setText("admin")  # للاختبار
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("fieldLabel")
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setObjectName("inputField")
        self.password_input.setText("123456")  # للاختبار
        
        # خانة تذكر كلمة المرور
        self.remember_checkbox = QCheckBox("تذكر كلمة المرور")
        self.remember_checkbox.setObjectName("checkBox")
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.handle_login)
        
        # زر الخروج
        self.exit_button = QPushButton("خروج")
        self.exit_button.setObjectName("exitButton")
        self.exit_button.clicked.connect(self.close)
        
        # تخطيط الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.addWidget(self.exit_button)
        buttons_layout.addWidget(self.login_button)
        
        # مساحة فارغة
        spacer2 = QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding)
        
        # إضافة العناصر للتخطيط
        login_layout.addWidget(logo_label)
        login_layout.addWidget(title_label)
        login_layout.addWidget(subtitle_label)
        login_layout.addItem(spacer1)
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)
        login_layout.addWidget(self.remember_checkbox)
        login_layout.addLayout(buttons_layout)
        login_layout.addItem(spacer2)
        
        # إضافة الإطار للتخطيط الرئيسي
        main_layout.addWidget(login_frame)
        
        self.setLayout(main_layout)
        
        # ربط Enter بتسجيل الدخول
        self.password_input.returnPressed.connect(self.handle_login)
        self.username_input.returnPressed.connect(self.handle_login)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QWidget {
            font-family: 'Segoe UI', Arial, sans-serif;
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        #loginFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 #2d2d2d, stop:1 #1a1a1a);
            border-radius: 15px;
            border: 2px solid #3d3d3d;
        }
        
        #titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: #4a9eff;
            margin-bottom: 5px;
        }
        
        #subtitleLabel {
            font-size: 14px;
            color: #cccccc;
            margin-bottom: 20px;
        }
        
        #fieldLabel {
            font-size: 12px;
            color: #cccccc;
            margin-bottom: 5px;
        }
        
        #inputField {
            padding: 12px 15px;
            border: 2px solid #3d3d3d;
            border-radius: 8px;
            background-color: #2d2d2d;
            color: #ffffff;
            font-size: 14px;
            min-height: 20px;
        }
        
        #inputField:focus {
            border-color: #4a9eff;
            background-color: #333333;
        }
        
        #checkBox {
            color: #cccccc;
            font-size: 12px;
        }
        
        #checkBox::indicator {
            width: 18px;
            height: 18px;
        }
        
        #checkBox::indicator:unchecked {
            border: 2px solid #3d3d3d;
            background-color: #2d2d2d;
            border-radius: 3px;
        }
        
        #checkBox::indicator:checked {
            border: 2px solid #4a9eff;
            background-color: #4a9eff;
            border-radius: 3px;
        }
        
        #loginButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #4a9eff, stop:1 #357abd);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            padding: 12px 30px;
            min-width: 120px;
        }
        
        #loginButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5ba6ff, stop:1 #4285d1);
        }
        
        #loginButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #3d8bef, stop:1 #2a6ba3);
        }
        
        #exitButton {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #6d6d6d, stop:1 #4d4d4d);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            padding: 12px 30px;
            min-width: 120px;
        }
        
        #exitButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #7d7d7d, stop:1 #5d5d5d);
        }
        
        #exitButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #5d5d5d, stop:1 #3d3d3d);
        }
        """
        
        self.setStyleSheet(style)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text().strip()
        
        if not username or not password:
            self.show_message("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور", QMessageBox.Warning)
            return
        
        # تعطيل زر تسجيل الدخول مؤقتاً
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري التحقق...")
        
        # التحقق من بيانات المستخدم
        user = User.authenticate(username, password)
        
        if user:
            # تحديث آخر تسجيل دخول
            user.update_last_login()
            
            self.current_user = user
            self.show_message("نجح", f"مرحباً {user.full_name}", QMessageBox.Information)
            
            # إخفاء نافذة تسجيل الدخول وفتح النافذة الرئيسية
            self.hide()
            self.open_main_window()
            
        else:
            self.show_message("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة", QMessageBox.Critical)
        
        # إعادة تفعيل زر تسجيل الدخول
        self.login_button.setEnabled(True)
        self.login_button.setText("تسجيل الدخول")
    
    def open_main_window(self):
        """فتح النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.current_user)
            self.main_window.show()
            
            # إغلاق نافذة تسجيل الدخول عند إغلاق النافذة الرئيسية
            self.main_window.closed.connect(self.show)
            
        except Exception as e:
            self.show_message("خطأ", f"خطأ في فتح النافذة الرئيسية: {e}", QMessageBox.Critical)
            self.show()
    
    def show_message(self, title: str, message: str, icon=QMessageBox.Information):
        """عرض رسالة للمستخدم"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(icon)
        msg_box.setLayoutDirection(Qt.RightToLeft)
        msg_box.exec_()
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def showEvent(self, event):
        """عند عرض النافذة"""
        super().showEvent(event)
        self.center_on_screen()
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        reply = QMessageBox.question(
            self, 'تأكيد الخروج',
            'هل تريد إغلاق البرنامج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if self.main_window:
                self.main_window.close()
            event.accept()
            QApplication.quit()
        else:
            event.ignore()
